<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Restaurant POS Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>

<body class="bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <!-- Sidebar -->
    <aside id="sidebar"
        class="fixed top-0 left-0 z-40 w-64 h-screen bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 transform -translate-x-full lg:translate-x-0 transition-transform duration-300">
        <div class="h-full px-3 py-4 overflow-y-auto">
            <!-- Logo -->
            <div class="flex items-center mb-8 px-2">
                <i class="fas fa-utensils text-2xl text-blue-600 mr-3"></i>
                <span class="text-xl font-bold text-gray-800 dark:text-white">RestaurantPOS</span>
            </div>

            <!-- Navigation Menu -->
            <nav class="space-y-2">
                <div class="nav-item" data-target="dashboard">
                    <a href="#"
                        class="flex items-center p-2 text-gray-900 dark:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                        <i
                            class="fas fa-tachometer-alt w-5 h-5 text-gray-500 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                        <span class="ml-3" data-translate="dashboard">Dashboard</span>
                    </a>
                </div>

                <div class="nav-item" data-target="orders">
                    <a href="#"
                        class="flex items-center p-2 text-gray-900 dark:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                        <i
                            class="fas fa-shopping-cart w-5 h-5 text-gray-500 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                        <span class="ml-3" data-translate="orders">Orders</span>
                        <span
                            class="ml-auto bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">12</span>
                    </a>
                </div>

                <div class="nav-group">
                    <button
                        class="nav-toggle flex items-center w-full p-2 text-gray-900 dark:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                        <i
                            class="fas fa-utensils w-5 h-5 text-gray-500 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                        <span class="ml-3" data-translate="menu">Menu</span>
                        <i class="fas fa-chevron-down ml-auto transition-transform duration-200"></i>
                    </button>
                    <div class="nav-submenu hidden ml-6 mt-2 space-y-1">
                        <div class="nav-item" data-target="menu-items">
                            <a href="#"
                                class="flex items-center p-2 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                                <span data-translate="menu-items">Menu Items</span>
                            </a>
                        </div>
                        <div class="nav-item" data-target="categories">
                            <a href="#"
                                class="flex items-center p-2 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                                <span data-translate="categories">Categories</span>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="nav-item" data-target="inventory">
                    <a href="#"
                        class="flex items-center p-2 text-gray-900 dark:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                        <i
                            class="fas fa-boxes w-5 h-5 text-gray-500 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                        <span class="ml-3" data-translate="inventory">Inventory</span>
                    </a>
                </div>

                <div class="nav-item" data-target="customers">
                    <a href="#"
                        class="flex items-center p-2 text-gray-900 dark:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                        <i
                            class="fas fa-users w-5 h-5 text-gray-500 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                        <span class="ml-3" data-translate="customers">Customers</span>
                    </a>
                </div>

                <div class="nav-group">
                    <button
                        class="nav-toggle flex items-center w-full p-2 text-gray-900 dark:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                        <i
                            class="fas fa-chart-bar w-5 h-5 text-gray-500 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                        <span class="ml-3" data-translate="reports">Reports</span>
                        <i class="fas fa-chevron-down ml-auto transition-transform duration-200"></i>
                    </button>
                    <div class="nav-submenu hidden ml-6 mt-2 space-y-1">
                        <div class="nav-item" data-target="sales-report">
                            <a href="#"
                                class="flex items-center p-2 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                                <span data-translate="sales-report">Sales Report</span>
                            </a>
                        </div>
                        <div class="nav-item" data-target="analytics">
                            <a href="#"
                                class="flex items-center p-2 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                                <span data-translate="analytics">Analytics</span>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="nav-item" data-target="settings">
                    <a href="#"
                        class="flex items-center p-2 text-gray-900 dark:text-white rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group">
                        <i
                            class="fas fa-cog w-5 h-5 text-gray-500 group-hover:text-gray-900 dark:group-hover:text-white"></i>
                        <span class="ml-3" data-translate="settings">Settings</span>
                    </a>
                </div>
            </nav>
        </div>
    </aside>

    <!-- Main Content -->
    <div class="lg:ml-64">
        <!-- Top Navigation -->
        <nav class="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <button id="sidebar-toggle"
                        class="lg:hidden p-2 text-gray-500 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="page-title" class="ml-4 text-xl font-semibold text-gray-800 dark:text-white"
                        data-translate="dashboard">Dashboard</h1>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Notifications -->
                    <div class="relative">
                        <button id="notifications-toggle" class="relative p-2 text-gray-500 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                        </button>
                        <div id="notifications-dropdown" class="hidden absolute right-0 mt-2 w-80 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600 max-h-96 overflow-y-auto">
                            <div class="p-4 border-b border-gray-200 dark:border-gray-600">
                                <h3 class="text-sm font-semibold text-gray-900 dark:text-white">Notifications</h3>
                            </div>
                            <div class="divide-y divide-gray-200 dark:divide-gray-600">
                                <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-shopping-cart text-blue-500"></i>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <p class="text-sm text-gray-900 dark:text-white">New order #007 from Table 5</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">2 minutes ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-exclamation-triangle text-yellow-500"></i>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <p class="text-sm text-gray-900 dark:text-white">Low stock alert: Tomatoes</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">15 minutes ago</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="p-4 hover:bg-gray-50 dark:hover:bg-gray-600">
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-check-circle text-green-500"></i>
                                        </div>
                                        <div class="ml-3 flex-1">
                                            <p class="text-sm text-gray-900 dark:text-white">Order #005 completed</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">1 hour ago</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="p-4 border-t border-gray-200 dark:border-gray-600">
                                <button class="w-full text-center text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                                    View all notifications
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Language Dropdown -->
                    <div class="relative">
                        <button id="language-toggle"
                            class="flex items-center p-2 text-gray-500 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-globe mr-2"></i>
                            <span id="current-language">EN</span>
                            <i class="fas fa-chevron-down ml-1"></i>
                        </button>
                        <div id="language-dropdown"
                            class="hidden absolute right-0 mt-2 w-32 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600">
                            <a href="#"
                                class="language-option block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600"
                                data-lang="en">English</a>
                            <a href="#"
                                class="language-option block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600"
                                data-lang="ar">العربية</a>
                            <a href="#"
                                class="language-option block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600"
                                data-lang="es">Español</a>
                        </div>
                    </div>

                    <!-- Theme Toggle -->
                    <button id="theme-toggle"
                        class="p-2 text-gray-500 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i id="theme-icon-light" class="fas fa-moon"></i>
                        <i id="theme-icon-dark" class="fas fa-sun hidden"></i>
                    </button>

                    <!-- Profile Dropdown -->
                    <div class="relative">
                        <button id="profile-toggle"
                            class="flex items-center p-2 text-gray-500 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700">
                            <img class="w-8 h-8 rounded-full" src="https://via.placeholder.com/32" alt="Profile">
                            <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">John Doe</span>
                            <i class="fas fa-chevron-down ml-1"></i>
                        </button>
                        <div id="profile-dropdown"
                            class="hidden absolute right-0 mt-2 w-48 bg-white dark:bg-gray-700 rounded-lg shadow-lg border border-gray-200 dark:border-gray-600">
                            <a href="#"
                                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                <i class="fas fa-user mr-2"></i>Profile
                            </a>
                            <a href="#"
                                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                <i class="fas fa-cog mr-2"></i>Settings
                            </a>
                            <hr class="my-1 border-gray-200 dark:border-gray-600">
                            <a href="#"
                                class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600">
                                <i class="fas fa-sign-out-alt mr-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <main class="p-6">
            <div id="content-area">
                <!-- Dashboard content will be loaded here -->
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>

</html>