// Restaurant POS Dashboard JavaScript

class RestaurantPOS {
    constructor() {
        this.currentLanguage = 'en';
        this.translations = {};
        this.activeNavItem = 'dashboard';
        this.sidebarOpen = false; // Track sidebar state
        this.isLargeScreen = window.innerWidth >= 1024;

        this.init();
    }

    init() {
        this.loadTranslations();
        this.initSidebar();
        this.bindEvents();
        this.loadContent('dashboard');
        // this.showToast('Welcome to Restaurant POS!', 'success');
    }

    initSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        this.isLargeScreen = window.innerWidth >= 1024;
        const isRTL = document.documentElement.getAttribute('dir') === 'rtl';

        // Set default sidebar state based on screen size
        if (this.isLargeScreen) {
            // Desktop: sidebar open by default
            this.sidebarOpen = true;
            sidebar.classList.remove('-translate-x-full', 'translate-x-full');
            overlay.classList.add('hidden');
            this.updateMainContentMargin(true);
        } else {
            // Mobile: sidebar closed by default
            this.sidebarOpen = false;
            if (isRTL) {
                sidebar.classList.remove('-translate-x-full');
                sidebar.classList.add('translate-x-full');
            } else {
                sidebar.classList.remove('translate-x-full');
                sidebar.classList.add('-translate-x-full');
            }
            overlay.classList.add('hidden');
            this.updateMainContentMargin(false);
        }
    }

    loadTranslations() {
        this.translations = {
            en: {
                dashboard: 'Dashboard',
                orders: 'Orders',
                menu: 'Menu',
                'menu-items': 'Menu Items',
                categories: 'Categories',
                inventory: 'Inventory',
                customers: 'Customers',
                reports: 'Reports',
                'sales-report': 'Sales Report',
                analytics: 'Analytics',
                settings: 'Settings',
                'total-sales': 'Total Sales',
                'total-orders': 'Total Orders',
                'active-tables': 'Active Tables',
                'daily-revenue': 'Daily Revenue',
                'sales-overview': 'Sales Overview',
                'recent-orders': 'Recent Orders',
                'top-items': 'Top Selling Items'
            },
            ar: {
                dashboard: 'لوحة التحكم',
                orders: 'الطلبات',
                menu: 'القائمة',
                'menu-items': 'عناصر القائمة',
                categories: 'الفئات',
                inventory: 'المخزون',
                customers: 'العملاء',
                reports: 'التقارير',
                'sales-report': 'تقرير المبيعات',
                analytics: 'التحليلات',
                settings: 'الإعدادات',
                'total-sales': 'إجمالي المبيعات',
                'total-orders': 'إجمالي الطلبات',
                'active-tables': 'الطاولات النشطة',
                'daily-revenue': 'الإيرادات اليومية',
                'sales-overview': 'نظرة عامة على المبيعات',
                'recent-orders': 'الطلبات الأخيرة',
                'top-items': 'العناصر الأكثر مبيعاً'
            },
            es: {
                dashboard: 'Panel de Control',
                orders: 'Pedidos',
                menu: 'Menú',
                'menu-items': 'Elementos del Menú',
                categories: 'Categorías',
                inventory: 'Inventario',
                customers: 'Clientes',
                reports: 'Reportes',
                'sales-report': 'Reporte de Ventas',
                analytics: 'Análisis',
                settings: 'Configuración',
                'total-sales': 'Ventas Totales',
                'total-orders': 'Pedidos Totales',
                'active-tables': 'Mesas Activas',
                'daily-revenue': 'Ingresos Diarios',
                'sales-overview': 'Resumen de Ventas',
                'recent-orders': 'Pedidos Recientes',
                'top-items': 'Artículos Más Vendidos'
            }
        };
    }



    bindEvents() {
        // Sidebar toggle
        document.getElementById('sidebar-toggle').addEventListener('click', this.toggleSidebar.bind(this));

        // Sidebar overlay click to close
        document.getElementById('sidebar-overlay').addEventListener('click', this.closeSidebar.bind(this));

        // Navigation items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const target = item.dataset.target;
                this.setActiveNavItem(target);
                this.loadContent(target);
            });
        });

        // Navigation group toggles
        document.querySelectorAll('.nav-toggle').forEach(toggle => {
            toggle.addEventListener('click', this.toggleNavGroup);
        });

        // Language dropdown
        document.getElementById('language-toggle').addEventListener('click', this.toggleLanguageDropdown);
        document.querySelectorAll('.language-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                this.changeLanguage(option.dataset.lang);
            });
        });

        // Notifications dropdown
        document.getElementById('notifications-toggle').addEventListener('click', this.toggleNotificationsDropdown);

        // Profile dropdown
        document.getElementById('profile-toggle').addEventListener('click', this.toggleProfileDropdown);

        // Close dropdowns when clicking outside
        document.addEventListener('click', this.closeDropdowns);

        // Window resize handler
        window.addEventListener('resize', this.handleResize.bind(this));

        // Message handler for testing
        window.addEventListener('message', (event) => {
            if (event.data.action === 'changeLanguage') {
                this.changeLanguage(event.data.lang);
            } else if (event.data.action === 'toggleSidebar') {
                this.toggleSidebar();
            } else if (event.data.action === 'getSidebarState') {
                window.parent.postMessage({
                    type: 'sidebarState',
                    state: {
                        sidebarOpen: this.sidebarOpen,
                        isLargeScreen: this.isLargeScreen,
                        isRTL: document.documentElement.getAttribute('dir') === 'rtl',
                        sidebarClasses: document.getElementById('sidebar').className
                    }
                }, '*');
            }
        });
    }

    handleResize() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        const wasLargeScreen = this.isLargeScreen;
        this.isLargeScreen = window.innerWidth >= 1024;
        const isRTL = document.documentElement.getAttribute('dir') === 'rtl';

        // Only update if screen size category changed
        if (wasLargeScreen !== this.isLargeScreen) {
            if (this.isLargeScreen) {
                // Switching to desktop: show sidebar, hide overlay, restore scroll
                this.sidebarOpen = true;
                sidebar.classList.remove('-translate-x-full', 'translate-x-full');
                overlay.classList.add('hidden');
                document.body.style.overflow = '';
                this.updateMainContentMargin(true);
            } else {
                // Switching to mobile: hide sidebar, hide overlay, restore scroll
                this.sidebarOpen = false;
                if (isRTL) {
                    sidebar.classList.remove('-translate-x-full');
                    sidebar.classList.add('translate-x-full');
                } else {
                    sidebar.classList.remove('translate-x-full');
                    sidebar.classList.add('-translate-x-full');
                }
                overlay.classList.add('hidden');
                document.body.style.overflow = '';
                this.updateMainContentMargin(false);
            }
        }
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        const isRTL = document.documentElement.getAttribute('dir') === 'rtl';
        this.isLargeScreen = window.innerWidth >= 1024;

        // Debug logging for development
        console.log(`Toggle sidebar - RTL: ${isRTL}, Large screen: ${this.isLargeScreen}, Current state: ${this.sidebarOpen}`);

        if (this.isLargeScreen) {
            // Desktop behavior: toggle sidebar open/closed
            this.sidebarOpen = !this.sidebarOpen;

            if (this.sidebarOpen) {
                // Show sidebar
                sidebar.classList.remove('-translate-x-full', 'translate-x-full');
                console.log('Desktop: Showing sidebar');
            } else {
                // Hide sidebar
                if (isRTL) {
                    sidebar.classList.remove('-translate-x-full');
                    sidebar.classList.add('translate-x-full');
                    console.log('Desktop RTL: Hiding sidebar with translate-x-full');
                } else {
                    sidebar.classList.remove('translate-x-full');
                    sidebar.classList.add('-translate-x-full');
                    console.log('Desktop LTR: Hiding sidebar with -translate-x-full');
                }
            }

            // No overlay on desktop
            overlay.classList.add('hidden');
            this.updateMainContentMargin(this.sidebarOpen);
        } else {
            // Mobile behavior: toggle sidebar with overlay
            this.sidebarOpen = !this.sidebarOpen;

            if (this.sidebarOpen) {
                // Show sidebar
                sidebar.classList.remove('-translate-x-full', 'translate-x-full');
                overlay.classList.remove('hidden');
                // Prevent body scroll when sidebar is open on mobile
                document.body.style.overflow = 'hidden';
            } else {
                // Hide sidebar
                if (isRTL) {
                    sidebar.classList.add('translate-x-full');
                } else {
                    sidebar.classList.add('-translate-x-full');
                }
                overlay.classList.add('hidden');
                // Restore body scroll
                document.body.style.overflow = '';
            }

            // Main content doesn't need margin adjustment on mobile
            this.updateMainContentMargin(false);
        }
    }

    closeSidebar() {
        if (!this.isLargeScreen && this.sidebarOpen) {
            this.toggleSidebar();
        }
    }

    updateMainContentMargin(sidebarOpen) {
        const mainContent = document.getElementById('main-content');
        const isRTL = document.documentElement.getAttribute('dir') === 'rtl';

        // Remove all existing margin classes first
        mainContent.className = mainContent.className.replace(/\s*(lg:)?m[lr]-\d+/g, '');

        if (this.isLargeScreen && sidebarOpen) {
            // Desktop with sidebar open: add appropriate margin
            if (isRTL) {
                mainContent.className += ' lg:mr-64';
            } else {
                mainContent.className += ' lg:ml-64';
            }
        }
        // If sidebar is closed or mobile: no margin (already removed above)
    }

    toggleNavGroup(e) {
        e.preventDefault();
        const submenu = e.target.nextElementSibling;
        const chevron = e.target.querySelector('.fa-chevron-down');

        submenu.classList.toggle('hidden');
        chevron.classList.toggle('rotate-180');
    }

    setActiveNavItem(target) {
        // Remove active class from all nav items
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to current item
        const activeItem = document.querySelector(`[data-target="${target}"]`);
        if (activeItem) {
            activeItem.classList.add('active');
        }

        // Update page title
        const pageTitle = document.getElementById('page-title');
        const translation = this.translations[this.currentLanguage][target] || target;
        pageTitle.textContent = translation;
        pageTitle.dataset.translate = target;

        this.activeNavItem = target;
    }



    toggleLanguageDropdown(e) {
        e.stopPropagation();
        const dropdown = document.getElementById('language-dropdown');
        dropdown.classList.toggle('hidden');
    }

    toggleNotificationsDropdown(e) {
        e.stopPropagation();
        const dropdown = document.getElementById('notifications-dropdown');
        dropdown.classList.toggle('hidden');
    }

    toggleProfileDropdown(e) {
        e.stopPropagation();
        const dropdown = document.getElementById('profile-dropdown');
        dropdown.classList.toggle('hidden');
    }

    closeDropdowns(e) {
        if (!e.target.closest('#language-toggle')) {
            document.getElementById('language-dropdown').classList.add('hidden');
        }
        if (!e.target.closest('#notifications-toggle')) {
            document.getElementById('notifications-dropdown').classList.add('hidden');
        }
        if (!e.target.closest('#profile-toggle')) {
            document.getElementById('profile-dropdown').classList.add('hidden');
        }
    }

    changeLanguage(lang) {
        this.currentLanguage = lang;

        // Update language display
        const langMap = { en: 'EN', ar: 'AR', es: 'ES' };
        document.getElementById('current-language').textContent = langMap[lang];

        // Update RTL support and reinitialize sidebar
        if (lang === 'ar') {
            document.documentElement.setAttribute('dir', 'rtl');
        } else {
            document.documentElement.setAttribute('dir', 'ltr');
        }

        // Reinitialize sidebar with new direction
        this.initSidebar();

        // Update all translatable elements
        this.updateTranslations();

        // Close dropdown
        document.getElementById('language-dropdown').classList.add('hidden');

        this.showToast(`Language changed to ${lang.toUpperCase()}`, 'info');
    }

    updateTranslations() {
        document.querySelectorAll('[data-translate]').forEach(element => {
            const key = element.dataset.translate;
            if (this.translations[this.currentLanguage][key]) {
                element.textContent = this.translations[this.currentLanguage][key];
            }
        });
    }

    loadContent(section) {
        const contentArea = document.getElementById('content-area');

        switch (section) {
            case 'dashboard':
                contentArea.innerHTML = this.getDashboardContent();
                this.initCharts();
                break;
            case 'orders':
                contentArea.innerHTML = this.getOrdersContent();
                break;
            case 'menu-items':
                contentArea.innerHTML = this.getMenuItemsContent();
                break;
            case 'categories':
                contentArea.innerHTML = this.getCategoriesContent();
                break;
            case 'inventory':
                contentArea.innerHTML = this.getInventoryContent();
                break;
            case 'customers':
                contentArea.innerHTML = this.getCustomersContent();
                break;
            case 'sales-report':
                contentArea.innerHTML = this.getSalesReportContent();
                this.initReportCharts();
                break;
            case 'analytics':
                contentArea.innerHTML = this.getAnalyticsContent();
                this.initAnalyticsCharts();
                break;
            case 'settings':
                contentArea.innerHTML = this.getSettingsContent();
                break;
            default:
                contentArea.innerHTML = this.getDashboardContent();
                this.initCharts();
        }
    }

    getDashboardContent() {
        return `
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="stat-card bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                            <i class="fas fa-dollar-sign text-blue-600 dark:text-blue-400"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400" data-translate="total-sales">Total Sales</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">$12,345</p>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
                            <i class="fas fa-shopping-cart text-green-600 dark:text-green-400"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400" data-translate="total-orders">Total Orders</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">156</p>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                            <i class="fas fa-chair text-yellow-600 dark:text-yellow-400"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400" data-translate="active-tables">Active Tables</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">8/12</p>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900">
                            <i class="fas fa-chart-line text-purple-600 dark:text-purple-400"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600 dark:text-gray-400" data-translate="daily-revenue">Daily Revenue</p>
                            <p class="text-2xl font-bold text-gray-900 dark:text-white">$2,847</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Charts Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4" data-translate="sales-overview">Sales Overview</h3>
                    <div class="chart-container">
                        <canvas id="salesChart"></canvas>
                    </div>
                </div>
                
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4" data-translate="top-items">Top Selling Items</h3>
                    <div class="chart-container">
                        <canvas id="topItemsChart"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Recent Orders -->
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white" data-translate="recent-orders">Recent Orders</h3>
                </div>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
                        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                            <tr>
                                <th class="px-6 py-3">Order ID</th>
                                <th class="px-6 py-3">Table</th>
                                <th class="px-6 py-3">Items</th>
                                <th class="px-6 py-3">Total</th>
                                <th class="px-6 py-3">Status</th>
                                <th class="px-6 py-3">Time</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${this.getRecentOrdersRows()}
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    getRecentOrdersRows() {
        const orders = [
            { id: '#001', table: 'T-05', items: 'Burger, Fries, Coke', total: '$24.50', status: 'Preparing', time: '2 min ago' },
            { id: '#002', table: 'T-03', items: 'Pizza Margherita', total: '$18.00', status: 'Ready', time: '5 min ago' },
            { id: '#003', table: 'T-08', items: 'Pasta, Salad', total: '$32.00', status: 'Served', time: '8 min ago' },
            { id: '#004', table: 'T-01', items: 'Steak, Wine', total: '$65.00', status: 'Preparing', time: '12 min ago' },
            { id: '#005', table: 'T-12', items: 'Coffee, Cake', total: '$12.50', status: 'Ready', time: '15 min ago' }
        ];

        return orders.map(order => `
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td class="px-6 py-4 font-medium text-gray-900 dark:text-white">${order.id}</td>
                <td class="px-6 py-4">${order.table}</td>
                <td class="px-6 py-4">${order.items}</td>
                <td class="px-6 py-4 font-semibold">${order.total}</td>
                <td class="px-6 py-4">
                    <span class="px-2 py-1 text-xs font-medium rounded-full ${this.getStatusClass(order.status)}">
                        ${order.status}
                    </span>
                </td>
                <td class="px-6 py-4 text-gray-500">${order.time}</td>
            </tr>
        `).join('');
    }

    getStatusClass(status) {
        switch (status) {
            case 'Preparing':
                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
            case 'Ready':
                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
            case 'Served':
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
        }
    }

    getOrdersContent() {
        return `
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">All Orders</h3>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            <i class="fas fa-plus mr-2"></i>New Order
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                        <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
                            <h4 class="font-semibold text-yellow-800 dark:text-yellow-200">Pending Orders</h4>
                            <p class="text-2xl font-bold text-yellow-900 dark:text-yellow-100">8</p>
                        </div>
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                            <h4 class="font-semibold text-blue-800 dark:text-blue-200">In Progress</h4>
                            <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">12</p>
                        </div>
                        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                            <h4 class="font-semibold text-green-800 dark:text-green-200">Completed Today</h4>
                            <p class="text-2xl font-bold text-green-900 dark:text-green-100">156</p>
                        </div>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400">Order management interface would be implemented here with real-time updates and order tracking.</p>
                </div>
            </div>
        `;
    }

    getMenuItemsContent() {
        return `
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Menu Items</h3>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            <i class="fas fa-plus mr-2"></i>Add Item
                        </button>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        ${this.getMenuItemCards()}
                    </div>
                </div>
            </div>
        `;
    }

    getMenuItemCards() {
        const items = [
            { name: 'Classic Burger', price: '$12.99', category: 'Main Course', image: 'https://via.placeholder.com/200x150' },
            { name: 'Margherita Pizza', price: '$18.99', category: 'Pizza', image: 'https://via.placeholder.com/200x150' },
            { name: 'Caesar Salad', price: '$9.99', category: 'Salads', image: 'https://via.placeholder.com/200x150' },
            { name: 'Grilled Salmon', price: '$24.99', category: 'Seafood', image: 'https://via.placeholder.com/200x150' },
            { name: 'Chocolate Cake', price: '$7.99', category: 'Desserts', image: 'https://via.placeholder.com/200x150' },
            { name: 'Cappuccino', price: '$4.99', category: 'Beverages', image: 'https://via.placeholder.com/200x150' }
        ];

        return items.map(item => `
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow">
                <img src="${item.image}" alt="${item.name}" class="w-full h-32 object-cover rounded-lg mb-3">
                <h4 class="font-semibold text-gray-900 dark:text-white">${item.name}</h4>
                <p class="text-sm text-gray-600 dark:text-gray-400">${item.category}</p>
                <div class="flex justify-between items-center mt-3">
                    <span class="text-lg font-bold text-blue-600 dark:text-blue-400">${item.price}</span>
                    <button class="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700">
                        Edit
                    </button>
                </div>
            </div>
        `).join('');
    }

    getCategoriesContent() {
        return `
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Menu Categories</h3>
                    <p class="text-gray-600 dark:text-gray-400">Category management interface for organizing menu items.</p>
                </div>
            </div>
        `;
    }

    getInventoryContent() {
        return `
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Inventory Management</h3>
                    <p class="text-gray-600 dark:text-gray-400">Track ingredients, supplies, and stock levels.</p>
                </div>
            </div>
        `;
    }

    getCustomersContent() {
        return `
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Customer Management</h3>
                    <p class="text-gray-600 dark:text-gray-400">Manage customer information and loyalty programs.</p>
                </div>
            </div>
        `;
    }

    getSalesReportContent() {
        return `
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Daily Sales</h3>
                    <div class="chart-container">
                        <canvas id="dailySalesChart"></canvas>
                    </div>
                </div>
                <div class="bg-white dark:bg-gray-800 p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Monthly Revenue</h3>
                    <div class="chart-container">
                        <canvas id="monthlyRevenueChart"></canvas>
                    </div>
                </div>
            </div>
        `;
    }

    getAnalyticsContent() {
        return `
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Analytics Dashboard</h3>
                    <div class="chart-container">
                        <canvas id="analyticsChart"></canvas>
                    </div>
                </div>
            </div>
        `;
    }

    getSettingsContent() {
        return `
            <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Settings</h3>
                    <div class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Restaurant Name</label>
                            <input type="text" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white" value="My Restaurant">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Currency</label>
                            <select class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                                <option>USD ($)</option>
                                <option>EUR (€)</option>
                                <option>GBP (£)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tax Rate (%)</label>
                            <input type="number" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white" value="8.5">
                        </div>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            Save Settings
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    initCharts() {
        // Simple chart implementation using canvas
        this.drawSalesChart();
        this.drawTopItemsChart();
    }

    initReportCharts() {
        this.drawDailySalesChart();
        this.drawMonthlyRevenueChart();
    }

    initAnalyticsCharts() {
        this.drawAnalyticsChart();
    }

    drawSalesChart() {
        const canvas = document.getElementById('salesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;

        // Simple line chart
        const data = [120, 190, 300, 500, 200, 300, 450];
        const labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

        this.drawLineChart(ctx, data, labels, canvas.width, canvas.height);
    }

    drawTopItemsChart() {
        const canvas = document.getElementById('topItemsChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;

        // Simple bar chart
        const data = [45, 35, 25, 20, 15];
        const labels = ['Burger', 'Pizza', 'Salad', 'Pasta', 'Steak'];

        this.drawBarChart(ctx, data, labels, canvas.width, canvas.height);
    }

    drawDailySalesChart() {
        const canvas = document.getElementById('dailySalesChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;

        const data = [800, 1200, 900, 1500, 1100, 1300, 1600];
        const labels = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

        this.drawLineChart(ctx, data, labels, canvas.width, canvas.height);
    }

    drawMonthlyRevenueChart() {
        const canvas = document.getElementById('monthlyRevenueChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;

        const data = [25000, 28000, 32000, 30000, 35000, 38000];
        const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];

        this.drawBarChart(ctx, data, labels, canvas.width, canvas.height);
    }

    drawAnalyticsChart() {
        const canvas = document.getElementById('analyticsChart');
        if (!canvas) return;

        const ctx = canvas.getContext('2d');
        canvas.width = canvas.offsetWidth;
        canvas.height = canvas.offsetHeight;

        const data = [65, 25, 10];
        const labels = ['Dine-in', 'Takeout', 'Delivery'];
        const colors = ['#3B82F6', '#10B981', '#F59E0B'];

        this.drawPieChart(ctx, data, labels, colors, canvas.width, canvas.height);
    }

    drawLineChart(ctx, data, labels, width, height) {
        const padding = 40;
        const chartWidth = width - 2 * padding;
        const chartHeight = height - 2 * padding;

        const maxValue = Math.max(...data);
        const stepX = chartWidth / (data.length - 1);
        const stepY = chartHeight / maxValue;

        // Clear canvas
        ctx.clearRect(0, 0, width, height);

        // Draw axes
        ctx.strokeStyle = '#E5E7EB';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(padding, padding);
        ctx.lineTo(padding, height - padding);
        ctx.lineTo(width - padding, height - padding);
        ctx.stroke();

        // Draw line
        ctx.strokeStyle = '#3B82F6';
        ctx.lineWidth = 2;
        ctx.beginPath();

        data.forEach((value, index) => {
            const x = padding + index * stepX;
            const y = height - padding - value * stepY;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });

        ctx.stroke();

        // Draw points
        ctx.fillStyle = '#3B82F6';
        data.forEach((value, index) => {
            const x = padding + index * stepX;
            const y = height - padding - value * stepY;

            ctx.beginPath();
            ctx.arc(x, y, 4, 0, 2 * Math.PI);
            ctx.fill();
        });

        // Draw labels
        ctx.fillStyle = '#6B7280';
        ctx.font = '12px sans-serif';
        ctx.textAlign = 'center';

        labels.forEach((label, index) => {
            const x = padding + index * stepX;
            ctx.fillText(label, x, height - 10);
        });
    }

    drawBarChart(ctx, data, labels, width, height) {
        const padding = 40;
        const chartWidth = width - 2 * padding;
        const chartHeight = height - 2 * padding;

        const maxValue = Math.max(...data);
        const barWidth = chartWidth / data.length * 0.8;
        const barSpacing = chartWidth / data.length * 0.2;

        // Clear canvas
        ctx.clearRect(0, 0, width, height);

        // Draw axes
        ctx.strokeStyle = '#E5E7EB';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(padding, padding);
        ctx.lineTo(padding, height - padding);
        ctx.lineTo(width - padding, height - padding);
        ctx.stroke();

        // Draw bars
        ctx.fillStyle = '#3B82F6';

        data.forEach((value, index) => {
            const barHeight = (value / maxValue) * chartHeight;
            const x = padding + index * (barWidth + barSpacing) + barSpacing / 2;
            const y = height - padding - barHeight;

            ctx.fillRect(x, y, barWidth, barHeight);
        });

        // Draw labels
        ctx.fillStyle = '#6B7280';
        ctx.font = '12px sans-serif';
        ctx.textAlign = 'center';

        labels.forEach((label, index) => {
            const x = padding + index * (barWidth + barSpacing) + barSpacing / 2 + barWidth / 2;
            ctx.fillText(label, x, height - 10);
        });
    }

    drawPieChart(ctx, data, labels, colors, width, height) {
        const centerX = width / 2;
        const centerY = height / 2;
        const radius = Math.min(width, height) / 2 - 40;

        const total = data.reduce((sum, value) => sum + value, 0);
        let currentAngle = -Math.PI / 2;

        // Clear canvas
        ctx.clearRect(0, 0, width, height);

        // Draw pie slices
        data.forEach((value, index) => {
            const sliceAngle = (value / total) * 2 * Math.PI;

            ctx.fillStyle = colors[index];
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
            ctx.closePath();
            ctx.fill();

            // Draw labels
            const labelAngle = currentAngle + sliceAngle / 2;
            const labelX = centerX + Math.cos(labelAngle) * (radius * 0.7);
            const labelY = centerY + Math.sin(labelAngle) * (radius * 0.7);

            ctx.fillStyle = '#FFFFFF';
            ctx.font = '12px sans-serif';
            ctx.textAlign = 'center';
            ctx.fillText(`${labels[index]} (${value}%)`, labelX, labelY);

            currentAngle += sliceAngle;
        });
    }

    showToast(message, type = 'info') {
        const toastContainer = document.getElementById('toast-container');
        const toast = document.createElement('div');

        const typeClasses = {
            success: 'bg-green-500',
            error: 'bg-red-500',
            warning: 'bg-yellow-500',
            info: 'bg-blue-500'
        };

        const typeIcons = {
            success: 'fa-check-circle',
            error: 'fa-exclamation-circle',
            warning: 'fa-exclamation-triangle',
            info: 'fa-info-circle'
        };

        toast.className = `toast flex items-center p-4 text-white rounded-lg shadow-lg ${typeClasses[type]}`;
        toast.innerHTML = `
            <i class="fas ${typeIcons[type]} mr-3"></i>
            <span>${message}</span>
            <button class="ml-auto text-white hover:text-gray-200" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        toastContainer.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.classList.add('removing');
                setTimeout(() => toast.remove(), 300);
            }
        }, 5000);
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    new RestaurantPOS();
});