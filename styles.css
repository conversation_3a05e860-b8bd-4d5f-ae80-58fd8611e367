/* Custom styles for Restaurant POS Dashboard */

/* Sidebar Overlay Styles */
#sidebar-overlay {
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
}

#sidebar-overlay.hidden {
    opacity: 0;
    pointer-events: none;
}

/* Sidebar Transition Improvements */
#sidebar {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
}

/* Main Content Transition */
#main-content {
    transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: margin;
}

/* RTL Support */
[dir="rtl"] {
    direction: rtl;
}

/* Sidebar RTL positioning */
[dir="rtl"] #sidebar {
    left: auto !important;
    right: 0 !important;
    border-left: 1px solid rgb(229 231 235) !important;
    border-right: none !important;
}

[dir="rtl"] .dark #sidebar {
    border-left: 1px solid rgb(55 65 81) !important;
    border-right: none !important;
}

/* Force RTL sidebar positioning on all screen sizes */
[dir="rtl"] aside {
    left: auto !important;
    right: 0 !important;
}

/* RTL sidebar transforms - hidden state for mobile */
[dir="rtl"] #sidebar.-translate-x-full {
    transform: translateX(100%) !important;
}

[dir="rtl"] #sidebar.translate-x-full {
    transform: translateX(100%) !important;
}

/* RTL sidebar transforms - visible state */
[dir="rtl"] #sidebar:not(.-translate-x-full):not(.translate-x-full) {
    transform: translateX(0) !important;
}

/* LTR sidebar transforms for consistency */
[dir="ltr"] #sidebar.-translate-x-full {
    transform: translateX(-100%) !important;
}

[dir="ltr"] #sidebar:not(.-translate-x-full):not(.translate-x-full) {
    transform: translateX(0) !important;
}

/* Override Tailwind's responsive classes for RTL - Allow toggling on desktop */
@media (min-width: 1024px) {
    /* RTL sidebar visible state on desktop */
    [dir="rtl"] #sidebar:not(.-translate-x-full):not(.translate-x-full) {
        transform: translateX(0) !important;
    }

    /* RTL sidebar hidden state on desktop - allow hiding when toggled */
    [dir="rtl"] #sidebar.translate-x-full {
        transform: translateX(100%) !important;
    }
}

/* Main content RTL margins */
[dir="rtl"] .lg\:ml-64 {
    margin-left: 0 !important;
    margin-right: 16rem !important;
}

/* RTL responsive main content */
@media (min-width: 1024px) {
    [dir="rtl"] .lg\:ml-64 {
        margin-left: 0 !important;
        margin-right: 16rem !important;
    }
}

/* Spacing adjustments for RTL */
[dir="rtl"] .ml-3 {
    margin-left: 0;
    margin-right: 0.75rem;
}

[dir="rtl"] .ml-4 {
    margin-left: 0;
    margin-right: 1rem;
}

[dir="rtl"] .ml-auto {
    margin-left: 0;
    margin-right: auto;
}

[dir="rtl"] .mr-2 {
    margin-right: 0;
    margin-left: 0.5rem;
}

[dir="rtl"] .mr-3 {
    margin-right: 0;
    margin-left: 0.75rem;
}

[dir="rtl"] .mr-1 {
    margin-right: 0;
    margin-left: 0.25rem;
}

/* Dropdown positioning for RTL */
[dir="rtl"] .absolute.right-0 {
    right: auto;
    left: 0;
}

/* Flex direction adjustments */
[dir="rtl"] .space-x-4 > * + * {
    margin-left: 0;
    margin-right: 1rem;
}

/* Text alignment for RTL */
[dir="rtl"] .text-left {
    text-align: right;
}

/* Sidebar submenu indentation for RTL */
[dir="rtl"] .nav-submenu {
    margin-left: 0;
    margin-right: 1.5rem;
}

/* Smooth transitions */
.nav-item a {
    transition: all 0.2s ease-in-out;
}

.nav-item.active a {
    background-color: rgb(59 130 246);
    color: white;
}

.nav-item.active a i {
    color: white;
}

/* Toast animations */
.toast {
    animation: slideIn 0.3s ease-out;
}

.toast.removing {
    animation: slideOut 0.3s ease-in forwards;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Chart container */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Dark mode scrollbar */
.dark ::-webkit-scrollbar-track {
    background: #374151;
}

.dark ::-webkit-scrollbar-thumb {
    background: #6b7280;
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Performance optimizations */
.nav-item,
.nav-toggle,
button,
a {
    will-change: transform;
}

/* Sidebar performance optimizations */
#sidebar {
    transform: translateZ(0);
    backface-visibility: hidden;
}

#sidebar-overlay {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    #sidebar,
    #main-content,
    #sidebar-overlay {
        transition: none !important;
    }
}

/* Dropdown animations */
.dropdown-enter {
    opacity: 0;
    transform: scale(0.95);
}

.dropdown-enter-active {
    opacity: 1;
    transform: scale(1);
    transition: opacity 150ms ease-out, transform 150ms ease-out;
}

.dropdown-exit {
    opacity: 1;
    transform: scale(1);
}

.dropdown-exit-active {
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 150ms ease-in, transform 150ms ease-in;
}

/* Loading spinner */
.spinner {
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Card hover effects */
.stat-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .chart-container {
        height: 250px;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .nav-item a:hover {
        background-color: #000;
        color: #fff;
    }
    
    .dark .nav-item a:hover {
        background-color: #fff;
        color: #000;
    }
}

/* Notification badge animation */
.notification-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* RTL support for notification dropdown */
[dir="rtl"] #notifications-dropdown {
    right: auto;
    left: 0;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .notification-badge {
        animation: none;
    }
}