<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTL Sidebar Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .test-button { margin: 5px; padding: 10px 15px; background: #007bff; color: white; border: none; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .status { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; margin: 10px 0; }
        .console-output { background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 150px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>RTL Sidebar Toggle Test</h1>
    
    <div class="test-section">
        <h2>RTL Sidebar Issue Test</h2>
        <p>This test specifically addresses the RTL sidebar toggle issue where:</p>
        <ul>
            <li>Sidebar doesn't respond to toggle in RTL mode</li>
            <li>Navbar expands to full width incorrectly</li>
            <li>Main content margin doesn't adjust properly</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button class="test-button" onclick="switchToArabic()">Switch to Arabic (RTL)</button>
        <button class="test-button" onclick="switchToEnglish()">Switch to English (LTR)</button>
        <button class="test-button" onclick="toggleSidebar()">Toggle Sidebar</button>
        <button class="test-button" onclick="checkSidebarState()">Check Sidebar State</button>
        <button class="test-button" onclick="clearConsole()">Clear Console</button>
    </div>

    <div class="status" id="status">
        Ready to test RTL sidebar functionality.
    </div>

    <div class="test-section">
        <h3>Console Output:</h3>
        <div id="console-output" class="console-output"></div>
    </div>

    <iframe id="test-frame" src="http://localhost:8000"></iframe>

    <div class="test-section">
        <h2>RTL Test Steps</h2>
        <ol>
            <li>Click "Switch to Arabic (RTL)" to enable RTL mode</li>
            <li>Verify sidebar is positioned on the right side</li>
            <li>Click "Toggle Sidebar" to test toggle functionality</li>
            <li>Check that sidebar hides/shows properly</li>
            <li>Verify main content margin adjusts correctly</li>
            <li>Check console output for debugging information</li>
        </ol>
    </div>

    <script>
        let consoleOutput = document.getElementById('console-output');
        
        function log(message) {
            consoleOutput.innerHTML += new Date().toLocaleTimeString() + ': ' + message + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        function switchToArabic() {
            const frame = document.getElementById('test-frame');
            frame.contentWindow.postMessage({action: 'changeLanguage', lang: 'ar'}, '*');
            document.getElementById('status').innerHTML = 'Switched to Arabic (RTL). Test sidebar toggle functionality.';
            log('Switched to Arabic (RTL)');
        }

        function switchToEnglish() {
            const frame = document.getElementById('test-frame');
            frame.contentWindow.postMessage({action: 'changeLanguage', lang: 'en'}, '*');
            document.getElementById('status').innerHTML = 'Switched to English (LTR). Test sidebar toggle functionality.';
            log('Switched to English (LTR)');
        }

        function toggleSidebar() {
            const frame = document.getElementById('test-frame');
            frame.contentWindow.postMessage({action: 'toggleSidebar'}, '*');
            log('Toggled sidebar');
        }

        function checkSidebarState() {
            const frame = document.getElementById('test-frame');
            frame.contentWindow.postMessage({action: 'getSidebarState'}, '*');
            log('Requested sidebar state');
        }

        function clearConsole() {
            consoleOutput.innerHTML = '';
        }

        // Listen for messages from iframe
        window.addEventListener('message', (event) => {
            if (event.data.type === 'sidebarState') {
                log('Sidebar State: ' + JSON.stringify(event.data.state));
            } else if (event.data.type === 'console') {
                log('Console: ' + event.data.message);
            }
        });

        log('RTL Sidebar Test initialized');
    </script>
</body>
</html>
