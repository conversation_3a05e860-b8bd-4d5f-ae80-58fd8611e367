<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .test-button { margin: 5px; padding: 10px 15px; background: #007bff; color: white; border: none; cursor: pointer; }
        .test-button:hover { background: #0056b3; }
        .status { margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff; }
        iframe { width: 100%; height: 600px; border: 1px solid #ccc; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Sidebar Toggle Functionality Test</h1>
    
    <div class="test-section">
        <h2>Test Instructions</h2>
        <p>Use the iframe below to test the sidebar functionality. The tests should verify:</p>
        <ul>
            <li><strong>Desktop (≥1024px):</strong> Sidebar open by default, toggles open/closed, main content adjusts margins</li>
            <li><strong>Mobile (&lt;1024px):</strong> Sidebar closed by default, toggles with overlay, overlay closes sidebar</li>
            <li><strong>RTL Support:</strong> Works correctly for Arabic language</li>
            <li><strong>Resize:</strong> Maintains proper state when switching between desktop/mobile</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button class="test-button" onclick="resizeFrame(1200, 800)">Desktop View (1200x800)</button>
        <button class="test-button" onclick="resizeFrame(375, 667)">Mobile View (375x667)</button>
        <button class="test-button" onclick="resizeFrame(768, 1024)">Tablet View (768x1024)</button>
        <button class="test-button" onclick="switchToArabic()">Switch to Arabic (RTL)</button>
        <button class="test-button" onclick="switchToEnglish()">Switch to English (LTR)</button>
    </div>

    <div class="status" id="status">
        Ready to test. Current frame size: <span id="frame-size">1200x800</span>
    </div>

    <iframe id="test-frame" src="http://localhost:8000"></iframe>

    <div class="test-section">
        <h2>Test Checklist</h2>
        <div>
            <input type="checkbox" id="desktop-default"> <label for="desktop-default">Desktop: Sidebar open by default</label><br>
            <input type="checkbox" id="desktop-toggle"> <label for="desktop-toggle">Desktop: Sidebar toggles correctly</label><br>
            <input type="checkbox" id="desktop-margin"> <label for="desktop-margin">Desktop: Main content margin adjusts</label><br>
            <input type="checkbox" id="mobile-default"> <label for="mobile-default">Mobile: Sidebar closed by default</label><br>
            <input type="checkbox" id="mobile-overlay"> <label for="mobile-overlay">Mobile: Overlay appears when sidebar opens</label><br>
            <input type="checkbox" id="mobile-close"> <label for="mobile-close">Mobile: Clicking overlay closes sidebar</label><br>
            <input type="checkbox" id="rtl-support"> <label for="rtl-support">RTL: Arabic language works correctly</label><br>
            <input type="checkbox" id="resize-behavior"> <label for="resize-behavior">Resize: State persists correctly</label><br>
            <input type="checkbox" id="smooth-animation"> <label for="smooth-animation">Animations: Smooth and performant</label><br>
        </div>
    </div>

    <script>
        function resizeFrame(width, height) {
            const frame = document.getElementById('test-frame');
            frame.style.width = width + 'px';
            frame.style.height = height + 'px';
            document.getElementById('frame-size').textContent = width + 'x' + height;
            
            // Update status
            const status = document.getElementById('status');
            if (width >= 1024) {
                status.innerHTML = `Desktop view (${width}x${height}). Sidebar should be open by default.`;
            } else {
                status.innerHTML = `Mobile view (${width}x${height}). Sidebar should be closed by default with overlay support.`;
            }
        }

        function switchToArabic() {
            const frame = document.getElementById('test-frame');
            frame.contentWindow.postMessage({action: 'changeLanguage', lang: 'ar'}, '*');
            document.getElementById('status').innerHTML = 'Switched to Arabic (RTL). Check sidebar positioning and transforms.';
        }

        function switchToEnglish() {
            const frame = document.getElementById('test-frame');
            frame.contentWindow.postMessage({action: 'changeLanguage', lang: 'en'}, '*');
            document.getElementById('status').innerHTML = 'Switched to English (LTR). Check sidebar positioning and transforms.';
        }

        // Initialize with desktop view
        resizeFrame(1200, 800);
    </script>
</body>
</html>
